# CarbonCoin 开发日志

## 2025-08-22 主体提取功能实现 ✅

### 本次改动概览
基于Apple官方Vision框架，成功实现了智能主体提取功能，支持用户通过点击照片选择特定主体，并自动裁切保存。

### 核心文件变更

#### 修改文件
```
CarbonCoin/Services/Vision/ImageProcess.swift
- 重构为基于Apple官方API的实现
- 新增 generateOutput() 主函数
- 新增 subjectMask() 掩码生成
- 新增 instances() 点击位置映射
- 新增 extractSubject() 便捷方法

CarbonCoin/ViewModels/ImageProcessViewModel.swift
- 简化状态管理，移除复杂的mask数组
- 新增 selectedTapPosition 点击位置记录
- 新增 handleImageTap() 处理用户点击
- 新增 showSubjectSelection 选择状态

CarbonCoin/Views/Core/ImageProcessView.swift
- 实现点击选择主体交互
- 新增选中位置可视化指示器
- 新增图片坐标转换辅助方法
- 优化用户提示文本
```

### 技术实现特色

#### 1. Apple官方Vision框架集成
```swift
// 基于官方参考实现
let request = VNGenerateForegroundInstanceMaskRequest()
let handler = VNImageRequestHandler(ciImage: image)

// 点击位置映射到实例索引
func instances(atPoint point: CGPoint?, inObservation observation: VNInstanceMaskObservation) -> IndexSet {
    guard let point = point else {
        return observation.allInstances
    }

    let mask = observation.instanceMask
    let coords = VNImagePointForNormalizedPoint(point, ...)

    // 查找像素坐标处的实例标签
    let instanceLabel = pixels.load(fromByteOffset: ..., as: UInt8.self)
    return instanceLabel == 0 ? observation.allInstances : [Int(instanceLabel)]
}
```

#### 2. 智能主体选择交互
```swift
// 点击位置转换为标准化坐标
.onTapGesture { location in
    let imageFrame = getImageFrame(in: geometry, for: inputImage)
    let normalizedPoint = CGPoint(
        x: (location.x - imageFrame.minX) / imageFrame.width,
        y: (location.y - imageFrame.minY) / imageFrame.height
    )
    viewModel.handleImageTap(at: normalizedPoint)
}

// 可视化选中效果
Circle()
    .fill(Color.green.opacity(0.3))
    .frame(width: 40, height: 40)
    .overlay(Circle().stroke(Color.green, lineWidth: 3))
    .animation(.easeInOut(duration: 0.3), value: tapPosition)
```

#### 3. 自动裁切和保存
```swift
// 支持裁切到主体范围
func generateOutput(
    forInputImage inputImage: CIImage,
    backgroundImage: CIImage,
    tapPosition: CGPoint?,
    croppedToInstancesExtent: Bool = true
) -> CIImage?

// 便捷提取方法
func extractSubject(
    from image: UIImage,
    tapPosition: CGPoint? = nil,
    croppedToInstancesExtent: Bool = true
) -> UIImage?
```

### 用户体验优化

#### 交互流程
1. **导入图片** → 自动检测主体
2. **点击选择** → 显示绿色圆圈指示器
3. **确认提取** → 按钮文本动态更新
4. **自动裁切** → 保存到相册

#### 视觉反馈
- ✅ 绿色圆圈选中指示器
- ✅ 平滑动画过渡效果
- ✅ 动态按钮文本提示
- ✅ 智能用户引导文案

### 架构设计原则
- ✅ 严格遵循Apple官方API规范
- ✅ 保持MVVM架构清晰分离
- ✅ 响应式状态管理
- ✅ 用户友好的交互设计

## 2025-08-22 多主体选择功能升级 ✅

### 本次改动概览
基于用户反馈，成功实现了更直观的多主体选择交互体验，包括固定位置的选择指示器、多选支持和流畅的动画效果。

### 核心改进内容

#### 1. 智能主体中心位置计算 ✅
```swift
// 新增主体信息结构
struct SubjectInfo {
    let index: Int
    let centerPosition: CGPoint // 标准化坐标 (0-1)
    let boundingBox: CGRect     // 标准化坐标 (0-1)
}

// 计算所有主体的中心位置
func calculateSubjectCenters(from observation: VNInstanceMaskObservation) -> [SubjectInfo] {
    // 遍历像素数据，为每个实例收集像素位置
    // 计算边界框和中心位置
    // 返回标准化坐标
}
```

#### 2. 固定位置选择指示器 ✅
```swift
// 主体选择指示器组件
struct SubjectIndicator: View {
    let subject: ImageProcess.SubjectInfo
    let isSelected: Bool
    let imageFrame: CGRect
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            ZStack {
                // 外圈 + 内圈 + 选中标记
                Circle().fill(Color.green.opacity(isSelected ? 0.3 : 0.1))
                Circle().stroke(Color.green, lineWidth: isSelected ? 3 : 2)
                Circle().fill(Color.green).frame(width: isSelected ? 16 : 12)
                if isSelected {
                    Image(systemName: "checkmark").font(.system(size: 8, weight: .bold))
                }
            }
        }
        .scaleEffect(isSelected ? 1.1 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isSelected)
    }
}
```

#### 3. 多选状态管理 ✅
```swift
// ViewModel 多选支持
@Published var subjects: [ImageProcess.SubjectInfo] = []
@Published var selectedSubjectIndices: Set<Int> = []

func toggleSubjectSelection(_ index: Int) {
    if selectedSubjectIndices.contains(index) {
        selectedSubjectIndices.remove(index)
    } else {
        selectedSubjectIndices.insert(index)
    }
}

func selectAllSubjects() {
    selectedSubjectIndices = Set(subjects.map { $0.index })
}

func clearSelection() {
    selectedSubjectIndices.removeAll()
}
```

#### 4. 批量主体处理 ✅
```swift
// 多主体提取方法
func extractMultipleSubjects(
    from image: UIImage,
    selectedIndices: Set<Int>,
    croppedToInstancesExtent: Bool = true
) -> UIImage? {
    // 生成多主体掩码
    let mask = try result.generateScaledMaskForImage(
        forInstances: IndexSet(selectedIndices),
        from: handler
    )
    // 应用掩码和裁切
}
```

#### 5. 智能交互体验 ✅
```swift
// 最近主体查找
func findNearestSubject(at point: CGPoint, in subjects: [SubjectInfo], threshold: Double = 0.1) -> SubjectInfo? {
    // 计算点击位置与各主体中心的距离
    // 返回阈值内最近的主体
}

// 动态UI文本
private func getInstructionText() -> String {
    if viewModel.subjects.isEmpty {
        return "未检测到主体，或直接提取全部内容"
    } else if viewModel.selectedSubjectIndices.isEmpty {
        return "点击绿色圆点选择要提取的主体，或直接提取全部主体"
    } else {
        return "已选择 \(viewModel.selectedSubjectIndices.count) 个主体，点击「提取选中主体」继续"
    }
}
```

### 用户体验提升

#### 交互流程优化
1. **智能检测** → 自动计算所有主体的中心位置
2. **固定指示器** → 在每个主体中心显示绿色圆点
3. **点击选择** → 最近主体被选中，圆点变为完全不透明
4. **多选支持** → 可同时选择多个主体，支持全选/清除
5. **动态反馈** → 按钮文本和提示信息实时更新

#### 视觉效果增强
- ✅ 固定位置的绿色圆点指示器
- ✅ 选中/未选中状态的视觉区分
- ✅ 缩放和弹簧动画效果
- ✅ 选中标记（checkmark）显示
- ✅ 动态透明度和边框变化

#### 控制功能完善
- ✅ 全选按钮（一键选择所有主体）
- ✅ 清除按钮（一键取消所有选择）
- ✅ 选择计数显示
- ✅ 智能按钮文本更新

### 技术架构优势
- ✅ 保持MVVM架构清晰分离
- ✅ 响应式状态管理
- ✅ 高效的像素数据处理
- ✅ 流畅的动画性能
- ✅ 直观的用户交互设计

### 未来改进计划

#### 1. 背景替换功能
```swift
// 背景图片选择
@Published var backgroundImage: UIImage?

// 背景效果选项
enum BackgroundEffect: CaseIterable {
    case transparent
    case blur
    case solidColor
    case customImage
}

// 应用背景效果
func applyBackgroundEffect(_ effect: BackgroundEffect, to image: CIImage) -> CIImage
```

#### 2. 实时预览优化
```swift
// 实时预览模式
@Published var showLivePreview: Bool = false

// 预览质量设置
enum PreviewQuality {
    case low, medium, high
}

// 异步预览生成
func generatePreview(quality: PreviewQuality) async -> UIImage?
```

#### 3. 导出选项扩展
```swift
// 导出格式选项
enum ExportFormat: CaseIterable {
    case png, jpeg, heic
}

// 导出质量设置
struct ExportSettings {
    let format: ExportFormat
    let quality: Float
    let includeMetadata: Bool
}

// 批量导出
func exportImages(with settings: ExportSettings) async throws
```

#### 4. 高级编辑功能
```swift
// 主体边缘优化
func refineSubjectEdges(mask: CIImage, radius: Float) -> CIImage

// 主体阴影生成
func generateSubjectShadow(subject: CIImage, offset: CGPoint, blur: Float) -> CIImage

// 主体颜色调整
func adjustSubjectColors(subject: CIImage, brightness: Float, contrast: Float, saturation: Float) -> CIImage
```

## 2025-08-20 地图组件完整实现 ✅

### 本次改动概览
基于现有 CarbonCoin 项目架构，成功设计并实现了完整的地图组件系统，替换了 `FootprintView` 中的 `MapPlaceholderView`。

### 核心文件变更

#### 新增文件
```
CarbonCoin/
├── Services/Location/
│   └── UserLocationManager.swift          # 位置权限管理和定位服务
├── ViewModels/
│   └── MapViewModel.swift                 # 地图数据状态管理
└── Views/Core/
    └── MapView.swift                      # 地图UI组件
```

#### 修改文件
```
CarbonCoin/Views/Screens/FootprintView.swift
- MapEntryButton: NavigationLink → Button + fullScreenCover
- 集成 MapView 全屏展示
```

### 技术架构设计

#### 1. 位置服务层 (UserLocationManager)
```swift
@MainActor class UserLocationManager: ObservableObject {
    @Published var userLocation: CLLocationCoordinate2D?
    @Published var authorizationStatus: CLAuthorizationStatus
    @Published var locationError: String?
    
    // 核心方法
    func requestLocationPermission()
    func startLocationUpdates()
    func stopLocationUpdates()
}
```

#### 2. 数据管理层 (MapViewModel)
```swift
@MainActor class MapViewModel: ObservableObject {
    @Published var cameraPosition: MapCameraPosition
    @Published var userLocation: CLLocationCoordinate2D?
    @Published var currentMapStyleType: MapStyleType
    
    // 核心功能
    func moveToUserLocation()
    func toggleMapStyle()
    func zoomToLevel(_ level: Double)
}
```

#### 3. UI展示层 (MapView)
```swift
struct MapView: View {
    Map(position: $viewModel.cameraPosition) {
        // 用户位置标记
        if let userLocation = viewModel.userLocation {
            Annotation("我在这里", coordinate: userLocation) {
                // 脉冲动画位置标记
            }
        }
    }
    .mapControls { /* 地图控件 */ }
    .mapStyle(viewModel.mapStyle)
}
```

### 实现特色

#### 现代化技术栈
- ✅ SwiftUI Map + MapKit 集成
- ✅ MapCameraPosition 替代传统 MKCoordinateRegion
- ✅ @MainActor 并发安全
- ✅ Combine 响应式编程

#### 用户体验优化
- ✅ 脉冲动画位置标记
- ✅ 毛玻璃效果控制按钮
- ✅ 地图样式切换（标准/混合/卫星）
- ✅ 完善的权限引导流程

#### 架构设计原则
- ✅ 严格遵循 MVVM 模式
- ✅ 单一职责原则
- ✅ 依赖注入和状态管理
- ✅ 错误处理和用户反馈

## 未来扩展计划

### 1. 好友位置系统

#### 数据模型扩展
```swift
// Models/FriendLocation.swift
struct FriendLocation: Identifiable, Codable {
    let id: UUID
    let userId: String
    let username: String
    let coordinate: CLLocationCoordinate2D
    let lastUpdated: Date
    let isOnline: Bool
    let avatar: String?
}

// ViewModels/MapViewModel.swift 扩展
@Published var friendLocations: [FriendLocation] = []
@Published var showFriendLocations: Bool = true

func loadFriendLocations() async {
    // 从服务器获取好友位置数据
    let friends = await friendService.getFriendLocations()
    await MainActor.run {
        self.friendLocations = friends
    }
}
```

#### UI 集成
```swift
// MapView.swift 扩展
Map(position: $viewModel.cameraPosition) {
    // 用户位置
    if let userLocation = viewModel.userLocation { /* ... */ }
    
    // 好友位置标记
    ForEach(viewModel.friendLocations) { friend in
        Annotation(friend.username, coordinate: friend.coordinate) {
            FriendLocationMarker(friend: friend)
        }
    }
}

struct FriendLocationMarker: View {
    let friend: FriendLocation
    
    var body: some View {
        VStack(spacing: 4) {
            // 好友头像
            AsyncImage(url: URL(string: friend.avatar ?? "")) { image in
                image.resizable()
            } placeholder: {
                Image(systemName: "person.circle.fill")
            }
            .frame(width: 32, height: 32)
            .clipShape(Circle())
            .overlay(
                Circle().stroke(friend.isOnline ? .green : .gray, lineWidth: 2)
            )
            
            // 用户名标签
            Text(friend.username)
                .font(.caption)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(.ultraThinMaterial, in: Capsule())
        }
    }
}
```

### 2. 打卡点系统

#### 数据模型设计
```swift
// Models/CheckinPoint.swift
struct CheckinPoint: Identifiable, Codable {
    let id: UUID
    let name: String
    let coordinate: CLLocationCoordinate2D
    let category: CheckinCategory
    let carbonReward: Int
    let description: String
    let imageURL: String?
    let isCompleted: Bool
    let completedAt: Date?
}

enum CheckinCategory: String, CaseIterable, Codable {
    case park = "公园"
    case recycling = "回收站"
    case publicTransport = "公共交通"
    case greenBuilding = "绿色建筑"
    case carbonNeutral = "碳中和场所"
    
    var icon: String {
        switch self {
        case .park: return "leaf.fill"
        case .recycling: return "arrow.3.trianglepath"
        case .publicTransport: return "bus.fill"
        case .greenBuilding: return "building.2.fill"
        case .carbonNeutral: return "globe.asia.australia.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .park: return .green
        case .recycling: return .blue
        case .publicTransport: return .orange
        case .greenBuilding: return .purple
        case .carbonNeutral: return .brandGreen
        }
    }
}
```

#### 服务层扩展
```swift
// Services/CheckinService.swift
@MainActor
class CheckinService: ObservableObject {
    func getNearbyCheckinPoints(center: CLLocationCoordinate2D, radius: Double) async -> [CheckinPoint] {
        // 获取附近打卡点
    }
    
    func checkinAtPoint(_ point: CheckinPoint) async throws -> CheckinResult {
        // 执行打卡操作
    }
    
    func getUserCheckinHistory() async -> [CheckinPoint] {
        // 获取用户打卡历史
    }
}

struct CheckinResult {
    let success: Bool
    let carbonReward: Int
    let message: String
    let newLevel: Int?
}
```

#### UI 组件实现
```swift
// MapView.swift 打卡点集成
Map(position: $viewModel.cameraPosition) {
    // 现有标记...
    
    // 打卡点标记
    ForEach(viewModel.nearbyCheckinPoints) { point in
        Annotation(point.name, coordinate: point.coordinate) {
            CheckinPointMarker(point: point) {
                viewModel.selectedCheckinPoint = point
                viewModel.showCheckinSheet = true
            }
        }
    }
}
.sheet(isPresented: $viewModel.showCheckinSheet) {
    if let point = viewModel.selectedCheckinPoint {
        CheckinDetailSheet(point: point)
    }
}

struct CheckinPointMarker: View {
    let point: CheckinPoint
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            ZStack {
                // 背景圆圈
                Circle()
                    .fill(point.category.color.opacity(0.2))
                    .frame(width: 40, height: 40)
                
                // 图标
                Image(systemName: point.category.icon)
                    .font(.title2)
                    .foregroundColor(point.category.color)
                
                // 完成状态指示
                if point.isCompleted {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                        .offset(x: 12, y: -12)
                }
            }
        }
        .scaleEffect(point.isCompleted ? 0.8 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: point.isCompleted)
    }
}
```

### 3. 轨迹记录系统

#### 轨迹数据扩展
```swift
// ViewModels/MapViewModel.swift 轨迹功能
@Published var isRecordingTrack: Bool = false
@Published var currentTrack: Track?
@Published var trackHistory: [Track] = []

func startTrackRecording() {
    isRecordingTrack = true
    currentTrack = Track(id: UUID(), startTime: Date())
    // 开始记录轨迹点
}

func stopTrackRecording() {
    isRecordingTrack = false
    if let track = currentTrack {
        trackHistory.append(track)
        // 保存轨迹到本地/云端
    }
}
```

#### 轨迹显示
```swift
// MapView.swift 轨迹显示
Map(position: $viewModel.cameraPosition) {
    // 现有标记...
    
    // 当前录制轨迹
    if let currentTrack = viewModel.currentTrack {
        MapPolyline(coordinates: currentTrack.coordinates)
            .stroke(.brandGreen, lineWidth: 4)
    }
    
    // 历史轨迹
    ForEach(viewModel.selectedHistoryTracks) { track in
        MapPolyline(coordinates: track.coordinates)
            .stroke(.blue.opacity(0.6), lineWidth: 2)
    }
}
```

### 实现优先级

1. **Phase 1** (1-2周): 好友位置系统
   - 好友位置数据模型
   - 位置共享权限管理
   - 基础好友位置显示

2. **Phase 2** (2-3周): 打卡点系统
   - 打卡点数据和服务
   - 打卡点地图标记
   - 打卡交互和奖励

3. **Phase 3** (2-3周): 轨迹记录
   - 轨迹录制功能
   - 轨迹数据存储
   - 轨迹回放和分享

### 技术考虑

- **性能优化**: 大量标记点的聚合显示
- **数据同步**: CloudKit 集成和离线支持
- **隐私保护**: 位置数据加密和权限控制
- **电池优化**: 后台位置更新策略
