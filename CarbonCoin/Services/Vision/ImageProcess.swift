//
//  ImageProcess.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import UIKit
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

class ImageProcess {
    // MARK: - 主体识别
    func detectSubjects(in image: UIImage) -> VNInstanceMaskObservation? {
        guard let ciImage = CIImage(image: image) else { return nil }
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: ciImage)
        do {
            try handler.perform([request])
            return request.results?.first
        } catch {
            print("主体识别失败: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - 根据点击位置获取实例索引
    func instances(atPoint point: CGPoint?, inObservation observation: VNInstanceMaskObservation) -> IndexSet {
        guard let point = point else {
            return observation.allInstances
        }

        let mask = observation.instanceMask
        let coords = VNImagePointForNormalizedPoint(
            point,
            CVPixelBufferGetWidth(mask) - 1,
            CVPixelBufferGetHeight(mask) - 1
        )

        // 查找计算像素坐标处的实例标签
        CVPixelBufferLockBaseAddress(mask, .readOnly)

        let pixels = CVPixelBufferGetBaseAddress(mask)!
        let bytesPerRow = CVPixelBufferGetBytesPerRow(mask)
        let instanceLabel = pixels.load(
            fromByteOffset: Int(coords.y) * bytesPerRow + Int(coords.x),
            as: UInt8.self
        )

        CVPixelBufferUnlockBaseAddress(mask, .readOnly)

        return instanceLabel == 0 ? observation.allInstances : [Int(instanceLabel)]
    }

    // MARK: - 主体提取和裁切
    func generateOutput(
        forInputImage inputImage: CIImage,
        backgroundImage: CIImage,
        tapPosition: CGPoint?,
        croppedToInstancesExtent: Bool = true
    ) -> CIImage? {
        // 生成掩码
        guard let mask = subjectMask(fromImage: inputImage, atPoint: tapPosition) else {
            return nil
        }

        // 应用视觉效果
        return apply(
            mask: mask,
            toInputImage: inputImage,
            background: backgroundImage,
            croppedToInstancesExtent: croppedToInstancesExtent
        )
    }

    private func subjectMask(fromImage image: CIImage, atPoint point: CGPoint?) -> CIImage? {
        // 执行Vision请求
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: image)

        do {
            try handler.perform([request])
        } catch {
            return nil
        }

        guard let result = request.results?.first else { return nil }

        // 从点击获取实例索引
        let instances = self.instances(atPoint: point, inObservation: result)

        // 生成掩码
        do {
            let mask = try result.generateScaledMaskForImage(
                forInstances: instances,
                from: handler
            )
            return CIImage(cvPixelBuffer: mask)
        } catch {
            return nil
        }
    }

    private func apply(
        mask: CIImage,
        toInputImage inputImage: CIImage,
        background: CIImage,
        croppedToInstancesExtent: Bool
    ) -> CIImage? {
        let filter = CIFilter.blendWithMask()
        filter.inputImage = inputImage
        filter.maskImage = mask
        filter.backgroundImage = background

        guard let outputImage = filter.outputImage else { return nil }

        if croppedToInstancesExtent {
            // 裁切到主体范围
            return cropToSubjectExtent(outputImage, mask: mask)
        } else {
            return outputImage
        }
    }

    private func cropToSubjectExtent(_ image: CIImage, mask: CIImage) -> CIImage {
        // 计算掩码的边界框
        let extent = mask.extent
        return image.cropped(to: extent)
    }

    // MARK: - 便捷方法：提取主体并保存
    func extractSubject(
        from image: UIImage,
        tapPosition: CGPoint? = nil,
        croppedToInstancesExtent: Bool = true
    ) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }

        // 使用透明背景
        let backgroundImage = CIImage.empty()

        guard let outputCIImage = generateOutput(
            forInputImage: ciImage,
            backgroundImage: backgroundImage,
            tapPosition: tapPosition,
            croppedToInstancesExtent: croppedToInstancesExtent
        ) else {
            return nil
        }

        return convertToUIImage(ciImage: outputCIImage)
    }

    private func convertToUIImage(ciImage: CIImage) -> UIImage? {
        let context = CIContext(options: nil)
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            return nil
        }
        return UIImage(cgImage: cgImage)
    }

    // MARK: - 图片保存
    func saveImageToAlbum(_ image: UIImage, completion: @escaping (Bool, Error?) -> Void) {
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        completion(true, nil)
    }
}

extension CVPixelBuffer {
    var ciImage: CIImage {
        return CIImage(cvPixelBuffer: self)
    }
}
