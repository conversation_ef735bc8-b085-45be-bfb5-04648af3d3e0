//
//  ImageProcess.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import UIKit
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

class ImageProcess {
    // MARK: - 主体识别
    func detectMasks(in image: UIImage) -> [VNInstanceMaskObservation]? {
        guard let ciImage = CIImage(image: image) else { return nil }
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: ciImage)
        do {
            try handler.perform([request])
            return request.results?.compactMap { $0 as? VNInstanceMaskObservation }
        } catch {
            print("主体识别失败: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - 边界框计算
    func boundingBox(for mask: VNInstanceMaskObservation, in image: CIImage) -> CGRect? {
        do {
            let handler = VNImageRequestHandler(ciImage: image)
            let pixelBuffer = try mask.generateScaledMaskForImage(
                forInstances: [0], // 单个掩码的索引，VNInstanceMaskObservation 本身代表一个实例
                from: handler
            )
            
            let width = CVPixelBufferGetWidth(pixelBuffer)
            let height = CVPixelBufferGetHeight(pixelBuffer)
            
            var minX = width
            var maxX = 0
            var minY = height
            var maxY = 0
            
            CVPixelBufferLockBaseAddress(pixelBuffer, .readOnly)
            guard let baseAddress = CVPixelBufferGetBaseAddress(pixelBuffer) else {
                CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly)
                return nil
            }
            
            let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)
            let buffer = baseAddress.assumingMemoryBound(to: UInt8.self)
            
            for y in 0..<height {
                for x in 0..<width {
                    let pixel = buffer[y * bytesPerRow + x]
                    if pixel > 0 {
                        minX = min(minX, x)
                        maxX = max(maxX, x)
                        minY = min(minY, y)
                        maxY = max(maxY, y)
                    }
                }
            }
            
            CVPixelBufferUnlockBaseAddress(pixelBuffer, .readOnly)
            
            guard minX <= maxX, minY <= maxY else { return nil }
            
            let normalizedRect = CGRect(
                x: CGFloat(minX) / CGFloat(width),
                y: CGFloat(minY) / CGFloat(height),
                width: CGFloat(maxX - minX + 1) / CGFloat(width),
                height: CGFloat(maxY - minY + 1) / CGFloat(height)
            )
            
            return normalizedRect
        } catch {
            print("计算边界框失败: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - 主体提取
    func removeBackground(
        from image: UIImage,
        selectedMaskIndex: Int? = nil,
        masks: [VNInstanceMaskObservation]? = nil,
        targetSize: CGSize = CGSize(width: 300, height: 300)
    ) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }

        if let masks = masks, let selectedMaskIndex = selectedMaskIndex {
            return extractSelectedMask(from: ciImage, masks: masks, selectedIndex: selectedMaskIndex, targetSize: targetSize)
        } else {
            return extractSingleMask(from: ciImage, targetSize: targetSize)
        }
    }

    private func extractSelectedMask(
        from ciImage: CIImage,
        masks: [VNInstanceMaskObservation],
        selectedIndex: Int,
        targetSize: CGSize
    ) -> UIImage? {
        guard selectedIndex < masks.count else {
            print("选中索引超出范围: \(selectedIndex)")
            return nil
        }
        
        let mask = masks[selectedIndex]
        do {
            let maskImage = try mask.generateScaledMaskForImage(
                forInstances: [selectedIndex],
                from: VNImageRequestHandler(ciImage: ciImage)
            )
            guard let outputImage = applyMask(mask: CIImage(cvPixelBuffer: maskImage), to: ciImage) else {
                return nil
            }
            return resizeImage(outputImage, to: targetSize)
        } catch {
            print("提取选中主体失败: \(error.localizedDescription)")
            return nil
        }
    }

    private func extractSingleMask(from ciImage: CIImage, targetSize: CGSize) -> UIImage? {
        guard let maskImage = createMask(from: ciImage) else { return nil }
        guard let outputImage = applyMask(mask: maskImage, to: ciImage) else { return nil }
        return resizeImage(outputImage, to: targetSize)
    }

    private func createMask(from inputImage: CIImage) -> CIImage? {
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: inputImage)
        do {
            try handler.perform([request])
            if let result = request.results?.first {
                return try result.generateScaledMaskForImage(
                    forInstances: result.allInstances,
                    from: handler
                ).ciImage
            }
        } catch {
            print("创建掩码失败: \(error.localizedDescription)")
        }
        return nil
    }

    private func applyMask(mask: CIImage, to image: CIImage) -> UIImage? {
        let filter = CIFilter.blendWithMask()
        filter.inputImage = image
        filter.maskImage = mask
        filter.backgroundImage = CIImage.empty()
        guard let outputImage = filter.outputImage else { return nil }
        return convertToUIImage(ciImage: outputImage)
    }

    private func convertToUIImage(ciImage: CIImage) -> UIImage? {
        let context = CIContext(options: nil)
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            return nil
        }
        return UIImage(cgImage: cgImage)
    }

    private func resizeImage(_ image: UIImage, to targetSize: CGSize) -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        let resizedImage = renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: targetSize))
        }
        return resizedImage
    }

    // MARK: - 图片保存
    func saveImageToAlbum(_ image: UIImage, completion: @escaping (Bool, Error?) -> Void) {
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        completion(true, nil)
    }
}

extension CVPixelBuffer {
    var ciImage: CIImage {
        return CIImage(cvPixelBuffer: self)
    }
}
