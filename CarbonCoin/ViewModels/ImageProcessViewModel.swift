//
//  ImageProcessViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import PhotosUI
import Vision
import CoreImage
import Photos

class ImageProcessViewModel: ObservableObject {
    @Published var selectedPhoto: PhotosPickerItem?
    @Published var inputImage: UIImage?
    @Published var processedImage: UIImage?
    @Published var masks: [(mask: VNInstanceMaskObservation, boundingBox: CGRect)]?
    @Published var selectedMaskIndex: Int?
    @Published var showPhotoPicker = false
    @Published var showSaveAlert = false
    @Published var isProcessing = false
    @Published var showPermissionAlert = false // 新增：权限提示

    private let imageProcessingService = ImageProcess()

    @MainActor
    func checkPhotoLibraryPermission() async -> Bool {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        switch status {
        case .authorized, .limited:
            return true
        case .notDetermined:
            return await withCheckedContinuation { continuation in
                PHPhotoLibrary.requestAuthorization(for: .readWrite) { newStatus in
                    continuation.resume(returning: newStatus == .authorized || newStatus == .limited)
                }
            }
        case .denied, .restricted:
            showPermissionAlert = true
            return false
        @unknown default:
            return false
        }
    }

    @MainActor
    func loadImage() {
        Task {
            // 检查相册权限
            guard await checkPhotoLibraryPermission() else {
                print("相册权限未授予")
                return
            }

            guard let selectedPhoto = selectedPhoto,
                  let data = try? await selectedPhoto.loadTransferable(type: Data.self),
                  let uiImage = UIImage(data: data),
                  let ciImage = CIImage(image: uiImage) else {
                print("无法加载图片数据")
                return
            }

            inputImage = uiImage
            if let masks = imageProcessingService.detectMasks(in: uiImage) {
                self.masks = masks.enumerated().compactMap { (index, mask) in
                    if let boundingBox = imageProcessingService.boundingBox(for: mask, in: ciImage) {
                        return (mask: mask, boundingBox: boundingBox)
                    }
                    return nil
                }
            } else {
                self.masks = nil
            }
            processedImage = nil
            selectedMaskIndex = nil
        }
    }

    @MainActor
    func processImage() {
        guard let inputImage = inputImage else { return }
        isProcessing = true

        Task {
            processedImage = imageProcessingService.removeBackground(
                from: inputImage,
                selectedMaskIndex: selectedMaskIndex,
                masks: masks?.map { $0.mask }
            )
            isProcessing = false
        }
    }

    @MainActor
    func saveImage() {
        guard let processedImage = processedImage else { return }
        imageProcessingService.saveImageToAlbum(processedImage) { success, error in
            if success {
                self.showSaveAlert = true
            }
        }
    }
}
