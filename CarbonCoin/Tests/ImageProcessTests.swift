//
//  ImageProcessTests.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import XCTest
import Vision
import CoreImage
@testable import CarbonCoin

final class ImageProcessTests: XCTestCase {
    
    var imageProcessor: ImageProcess!
    
    override func setUpWithError() throws {
        imageProcessor = ImageProcess()
    }
    
    override func tearDownWithError() throws {
        imageProcessor = nil
    }
    
    // MARK: - 测试主体检测
    func testDetectSubjects() throws {
        // 创建测试图片
        let testImage = createTestImage()
        
        // 执行主体检测
        let observation = imageProcessor.detectSubjects(in: testImage)
        
        // 验证结果
        XCTAssertNotNil(observation, "应该能够检测到主体")
    }
    
    // MARK: - 测试点击位置映射
    func testInstancesAtPoint() throws {
        let testImage = createTestImage()
        
        guard let observation = imageProcessor.detectSubjects(in: testImage) else {
            XCTFail("无法检测到主体")
            return
        }
        
        // 测试中心点击
        let centerPoint = CGPoint(x: 0.5, y: 0.5)
        let instances = imageProcessor.instances(atPoint: centerPoint, inObservation: observation)
        
        XCTAssertFalse(instances.isEmpty, "中心点应该有实例")
        
        // 测试无点击（应该返回所有实例）
        let allInstances = imageProcessor.instances(atPoint: nil, inObservation: observation)
        XCTAssertEqual(allInstances, observation.allInstances, "无点击时应该返回所有实例")
    }
    
    // MARK: - 测试主体提取
    func testExtractSubject() throws {
        let testImage = createTestImage()
        
        // 测试无点击提取（提取所有主体）
        let extractedImage = imageProcessor.extractSubject(from: testImage)
        XCTAssertNotNil(extractedImage, "应该能够提取主体")
        
        // 测试指定点击位置提取
        let tapPosition = CGPoint(x: 0.5, y: 0.5)
        let extractedWithTap = imageProcessor.extractSubject(
            from: testImage,
            tapPosition: tapPosition,
            croppedToInstancesExtent: true
        )
        XCTAssertNotNil(extractedWithTap, "应该能够提取指定位置的主体")
    }
    
    // MARK: - 测试性能
    func testPerformanceDetectSubjects() throws {
        let testImage = createTestImage()
        
        measure {
            _ = imageProcessor.detectSubjects(in: testImage)
        }
    }
    
    func testPerformanceExtractSubject() throws {
        let testImage = createTestImage()
        
        measure {
            _ = imageProcessor.extractSubject(from: testImage)
        }
    }
    
    // MARK: - 辅助方法
    private func createTestImage() -> UIImage {
        // 创建一个简单的测试图片
        let size = CGSize(width: 300, height: 300)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // 绘制背景
            UIColor.white.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // 绘制一个圆形作为主体
            UIColor.blue.setFill()
            let circleRect = CGRect(x: 100, y: 100, width: 100, height: 100)
            context.cgContext.fillEllipse(in: circleRect)
            
            // 绘制一个矩形作为另一个主体
            UIColor.red.setFill()
            let rectRect = CGRect(x: 150, y: 150, width: 80, height: 60)
            context.fill(rectRect)
        }
    }
}
