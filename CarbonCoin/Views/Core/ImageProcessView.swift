//
//  ImageProcessView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import SwiftUI
import PhotosUI
import Photos

struct ImageProcessView: View {
    @StateObject private var viewModel = ImageProcessViewModel()
    @State private var showCamera = false

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                // 图片显示区域
                imageDisplayArea

                // 操作按钮
                HStack(spacing: 20) {
                    importButton
                    processButton
                    saveButton
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                .padding(.bottom, Theme.Spacing.tab)
            }
            .padding()
            .navigationTitle("主体提取")
            .sheet(isPresented: $showCamera) {
                ImagePicker(selectedImage: $viewModel.inputImage)
            }
            .photosPicker(isPresented: $viewModel.showPhotoPicker, selection: $viewModel.selectedPhoto, matching: .images)
            .alert("保存成功", isPresented: $viewModel.showSaveAlert) {
                But<PERSON>("确定", role: .cancel) { }
            }
            .alert("需要相册权限", isPresented: $viewModel.showPermissionAlert) {
                Button("前往设置") {
                    if let settingsURL = URL(string: UIApplication.openSettingsURLString) {
                        UIApplication.shared.open(settingsURL)
                    }
                }
                Button("取消", role: .cancel) { }
            } message: {
                Text("请在设置中授予相册访问权限以选择图片")
            }
            .overlay {
                if viewModel.isProcessing {
                    ProgressView()
                        .scaleEffect(2)
                }
            }
            // 监听 selectedPhoto 变化以触发加载
            .onChange(of: viewModel.selectedPhoto) { _ in
                Task {
                    await viewModel.loadImage()
                }
            }
        }
    }

    // MARK: - 子视图
    private var imageDisplayArea: some View {
        GeometryReader { geometry in
            Group {
                VStack {
                    if let processedImage = viewModel.processedImage {
                        Image(uiImage: processedImage)
                            .resizable()
                            .scaledToFit()
                            .frame(width: 300, height: 300) // 固定大小
                    } else if let inputImage = viewModel.inputImage {
                        ZStack {
                            Image(uiImage: inputImage)
                                .resizable()
                                .scaledToFit()
                                .frame(maxHeight: 400)
                            if let masks = viewModel.masks {
                                ForEach(0..<masks.count, id: \.self) { index in
                                    let box = masks[index].boundingBox
                                    Rectangle()
                                        .stroke(viewModel.selectedMaskIndex == index ? Color.green : Color.red, lineWidth: 3)
                                        .frame(
                                            width: box.width * geometry.size.width,
                                            height: box.height * geometry.size.height
                                        )
                                        .offset(
                                            x: box.origin.x * geometry.size.width - geometry.size.width / 2 + box.width * geometry.size.width / 2,
                                            y: -box.origin.y * geometry.size.height + geometry.size.height / 2 - box.height * geometry.size.height / 2
                                        )
                                        .onTapGesture {
                                            viewModel.selectedMaskIndex = index
                                        }
                                }
                            }
                        }
                    } else {
                        Image(systemName: "photo.on.rectangle")
                            .font(.system(size: 100))
                            .foregroundColor(.gray)
                            .frame(maxHeight: 400)
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .background(Color(.systemGroupedBackground))
            .cornerRadius(12)
        }
    }

    private var importButton: some View {
        Menu {
            Button {
                Task {
                    if await viewModel.checkPhotoLibraryPermission() {
                        showCamera = true
                    }
                }
            } label: {
                Label("拍照", systemImage: "camera")
            }

            Button {
                Task {
                    if await viewModel.checkPhotoLibraryPermission() {
                        viewModel.showPhotoPicker = true
                    }
                }
            } label: {
                Label("从相册选择", systemImage: "photo")
            }
        } label: {
            Text("导入图片")
                .frame(maxWidth: .infinity)
        }
    }

    private var processButton: some View {
        Button {
            viewModel.processImage()
        } label: {
            Text("提取主体")
                .frame(maxWidth: .infinity)
        }
        .disabled(viewModel.inputImage == nil || viewModel.isProcessing)
    }

    private var saveButton: some View {
        Button {
            viewModel.saveImage()
        } label: {
            Text("保存图片")
                .frame(maxWidth: .infinity)
        }
        .disabled(viewModel.processedImage == nil)
    }
}

#Preview {
    ImageProcessView()
}
